# 持仓表模型 (Position Model)

## 概述

本项目为量化交易系统设计并实现了一个完整的持仓表模型，用于记录和管理交易系统中的持仓信息。该模型与现有的订单表（order表）形成完整的交易记录体系，提供了持仓状态跟踪、盈亏计算、统计分析等核心功能。

## 功能特性

### ✅ 已完成功能

1. **完整的数据库模型设计**
   - 持仓表结构设计，包含25个核心字段
   - 支持SQLite数据库，使用SQLAlchemy ORM
   - 自动创建表和索引优化

2. **SQLAlchemy ORM模型**
   - 完整的Position模型类
   - 三个枚举类型：PositionSide、PositionStatus、PositionType
   - 内置盈亏计算方法和业务逻辑
   - 数据验证和类型转换功能

3. **数据访问对象(DAO)**
   - 基础CRUD操作
   - 丰富的查询方法（按合约、渠道、方向、状态等）
   - 盈亏分析查询（盈利/亏损持仓）
   - 批量价格更新功能
   - 复合条件搜索
   - 统计分析功能

4. **业务逻辑支持**
   - 持仓生命周期管理
   - 实时盈亏计算
   - 自动价格更新
   - 平仓操作处理
   - 收益率计算

5. **完整的测试验证**
   - 模型功能测试
   - DAO功能测试
   - 业务逻辑测试
   - 所有测试通过验证

6. **使用文档和示例**
   - 详细的使用指南
   - 完整的代码示例
   - API文档说明

## 文件结构

```
custom_api/
├── models/
│   ├── __init__.py          # 模型包初始化（已更新）
│   ├── position.py          # 持仓模型（新增）
│   ├── order.py             # 订单模型（已存在）
│   └── pending_order.py     # 挂单模型（已存在）
├── dao/
│   ├── __init__.py          # DAO包初始化（已更新）
│   ├── position_dao.py      # 持仓DAO（新增）
│   ├── order_dao.py         # 订单DAO（已存在）
│   ├── base_dao.py          # 基础DAO类（已存在）
│   └── pending_order_dao.py # 挂单DAO（已存在）
└── database/
    ├── __init__.py          # 数据库包初始化
    ├── config.py            # 数据库配置
    └── connection.py        # 数据库连接管理（已更新）

docs/
└── position_model_usage.md # 详细使用指南（新增）

examples/
└── position_example.py     # 使用示例（新增）

README_POSITION_MODEL.md    # 本文档（新增）
```

## 核心表结构

| 字段分类 | 主要字段 | 说明 |
|---------|---------|------|
| **基础信息** | id, position_id, open_order_id | 主键和关联信息 |
| **合约信息** | channel_code, symbol | 渠道和合约代码 |
| **持仓属性** | position_side, position_status, position_type | 方向、状态、类型 |
| **时间信息** | open_time, close_time, create_time, update_time | 时间戳记录 |
| **价格信息** | open_price, current_price, close_price | 价格数据 |
| **数量信息** | total_quantity, available_quantity, frozen_quantity | 持仓量管理 |
| **成本盈亏** | position_cost, market_value, unrealized_pnl, realized_pnl | 财务数据 |
| **费用信息** | commission, swap_fee | 交易成本 |
| **风控信息** | stop_loss_price, take_profit_price | 止损止盈 |
| **状态控制** | is_active, remark | 状态和备注 |

## 快速开始

### 1. 初始化数据库

```python
from custom_api.database.connection import init_database

# 初始化数据库（创建表）
init_database()
```

### 2. 创建持仓

```python
from custom_api.models import Position, PositionSide, PositionStatus, PositionType
from custom_api.dao import get_position_dao
from datetime import datetime
from decimal import Decimal

# 获取DAO实例
position_dao = get_position_dao()

# 创建持仓数据
position_data = {
    'position_id': 'POS20241212001',
    'open_order_id': 'ORD20241212001',
    'channel_code': 'CHANNEL001',
    'symbol': 'XAUUSD',
    'position_side': PositionSide.LONG,
    'position_status': PositionStatus.OPEN,
    'open_time': datetime.now(),
    'open_price': Decimal('2650.50'),
    'total_quantity': Decimal('1.0'),
    'available_quantity': Decimal('1.0'),
    'position_cost': Decimal('2650.50'),
    'is_active': True
}

# 创建持仓
position = position_dao.create(position_data)
```

### 3. 查询和分析

```python
# 查询活跃持仓
active_positions = position_dao.get_active_positions()

# 更新价格
price_updates = [{'symbol': 'XAUUSD', 'price': 2665.00}]
position_dao.update_current_prices(price_updates)

# 获取统计信息
stats = position_dao.get_statistics_by_symbol('XAUUSD')
overall_stats = position_dao.get_overall_statistics()

# 平仓操作
closed_position = position_dao.close_position(
    position_id='POS20241212001',
    close_price=Decimal('2670.00')
)
```

## 运行示例

```bash
# 运行完整示例
python examples/position_example.py
```

## 主要特性

### 1. 完整的持仓生命周期管理
- 开仓记录创建
- 实时价格更新
- 盈亏自动计算
- 平仓处理

### 2. 丰富的查询功能
- 多维度查询（合约、渠道、方向、状态）
- 盈亏分析查询
- 复合条件搜索
- 分页支持

### 3. 统计分析功能
- 按合约统计
- 整体持仓统计
- 胜率和收益率计算
- 多空持仓分析

### 4. 业务逻辑支持
- 自动盈亏计算
- 收益率计算
- 持仓状态管理
- 风控价格设置

### 5. 与订单表的集成
- 通过订单ID关联
- 完整的交易链路追踪
- 一致的数据结构设计

## 与现有系统的集成

### 与订单表的关系
- `open_order_id`: 关联开仓订单
- `close_order_id`: 关联平仓订单
- 形成完整的交易记录链

### 数据一致性
- 统一的渠道编号和合约代码
- 一致的枚举类型设计
- 相同的精度和数据类型

### 代码结构一致性
- 遵循相同的DAO模式
- 统一的命名规范
- 一致的错误处理

## 技术栈

- **数据库**: SQLite
- **ORM**: SQLAlchemy
- **语言**: Python 3.13+
- **架构**: DAO模式
- **精度**: DECIMAL(18,8)

## 测试验证

所有功能已通过完整测试：
- ✅ 模型创建和验证
- ✅ 数据库表创建
- ✅ CRUD操作
- ✅ 复合查询
- ✅ 统计分析
- ✅ 盈亏计算
- ✅ 价格更新
- ✅ 平仓操作
- ✅ 数据转换

## 性能优化

### 索引策略
- 持仓编号唯一索引
- 合约代码索引
- 持仓方向和状态索引
- 开仓时间索引
- 活跃状态索引

### 查询优化
- 分页查询支持
- 复合条件优化
- 统计查询优化

## 扩展性

### 支持的扩展
- 新的持仓类型
- 更多统计维度
- 自定义计算方法
- 多数据库支持

### 预留接口
- 自定义盈亏计算
- 扩展统计指标
- 插件化风控规则

## 文档

- [详细使用指南](docs/position_model_usage.md)
- [代码示例](examples/position_example.py)
- [订单模型文档](README_ORDER_MODEL.md)

## 业务价值

1. **完整的持仓管理**: 提供从开仓到平仓的完整生命周期管理
2. **实时盈亏跟踪**: 支持实时价格更新和盈亏计算
3. **丰富的分析功能**: 多维度统计分析，支持投资决策
4. **高性能查询**: 优化的索引和查询策略
5. **易于集成**: 与现有订单系统无缝集成

---

**创建时间**: 2024-12-12  
**版本**: 1.0.0  
**状态**: ✅ 完成并测试通过  
**与订单表集成**: ✅ 完全兼容
