# 订单表模型 (Order Model)

## 概述

本项目为量化交易系统创建了一个完整的订单表模型，用于记录交易系统中的订单成交信息。该模型包括数据库表结构、SQLAlchemy ORM模型、数据访问对象(DAO)以及完整的使用示例。

## 功能特性

### ✅ 已完成功能

1. **数据库模型设计**
   - 完整的订单表结构设计
   - 支持SQLite数据库
   - 自动创建表和索引

2. **SQLAlchemy ORM模型**
   - 完整的Order模型类
   - 枚举类型支持（SideType, EffectType, OrderType）
   - 数据验证和类型转换
   - 字典转换方法（to_dict/from_dict）

3. **数据访问对象(DAO)**
   - 基础CRUD操作
   - 复合条件查询
   - 统计分析功能
   - 批量操作支持

4. **完整的测试验证**
   - 模型功能测试
   - DAO功能测试
   - 所有测试通过验证

5. **使用文档和示例**
   - 详细的使用指南
   - 完整的代码示例
   - API文档说明

## 文件结构

```
custom_api/
├── models/
│   ├── __init__.py          # 模型包初始化
│   ├── order.py             # 订单模型
│   └── pending_order.py     # 挂单模型（已存在）
├── dao/
│   ├── __init__.py          # DAO包初始化
│   ├── base_dao.py          # 基础DAO类
│   ├── order_dao.py         # 订单DAO
│   └── pending_order_dao.py # 挂单DAO（已存在）
└── database/
    ├── __init__.py          # 数据库包初始化
    ├── config.py            # 数据库配置
    └── connection.py        # 数据库连接管理

docs/
└── order_model_usage.md    # 详细使用指南

examples/
└── order_example.py        # 使用示例

README_ORDER_MODEL.md        # 本文档
```

## 表结构

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| `id` | INTEGER | - | NOT NULL | AUTO_INCREMENT | 主键，自增ID |
| `order_no` | VARCHAR | 64 | NOT NULL | - | 订单号，关联原始订单 |
| `execution_id` | VARCHAR | 64 | NOT NULL | - | 成交号，唯一标识每笔成交 |
| `execution_time` | DATETIME | - | NOT NULL | - | 成交时间 |
| `channel_code` | VARCHAR | 32 | NOT NULL | - | 渠道编号 |
| `symbol` | VARCHAR | 32 | NOT NULL | - | 合约代码 |
| `side` | ENUM | - | NOT NULL | - | 成交方向 (BUY/SELL) |
| `execution_price` | DECIMAL | 18,8 | NOT NULL | - | 成交价格 |
| `execution_quantity` | DECIMAL | 18,8 | NOT NULL | - | 成交量 |
| `effect_type` | ENUM | - | NOT NULL | - | 开平仓类型 |
| `order_type` | ENUM | - | NOT NULL | - | 订单类型 |
| `value_date` | VARCHAR | 8 | NULL | - | 近端交割日 (yyyyMMdd) |
| `maturity_date` | VARCHAR | 8 | NULL | - | 远端交割日 (yyyyMMdd) |
| `commission` | DECIMAL | 18,8 | NULL | 0.00 | 手续费 |
| `create_time` | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录创建时间 |
| `update_time` | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录更新时间 |
| `remark` | TEXT | - | NULL | - | 备注信息 |

## 快速开始

### 1. 初始化数据库

```python
from custom_api.database.connection import init_database

# 初始化数据库（创建表）
init_database()
```

### 2. 创建订单

```python
from custom_api.models import Order, SideType, EffectType, OrderType
from custom_api.dao import get_order_dao
from datetime import datetime
from decimal import Decimal

# 获取DAO实例
order_dao = get_order_dao()

# 创建订单数据
order_data = {
    'order_no': 'ORD20241212001',
    'execution_id': 'EXE20241212001001',
    'execution_time': datetime.now(),
    'channel_code': 'CHANNEL001',
    'symbol': 'XAUUSD',
    'side': SideType.BUY,
    'execution_price': Decimal('2650.50'),
    'execution_quantity': Decimal('1.0'),
    'effect_type': EffectType.OPEN,
    'order_type': OrderType.MARKET,
    'commission': Decimal('5.30'),
    'remark': '黄金买入开仓'
}

# 创建订单
order = order_dao.create(order_data)
```

### 3. 查询订单

```python
# 根据ID查询
order = order_dao.get(1)

# 根据成交号查询
order = order_dao.get_by_execution_id('EXE20241212001001')

# 根据订单号查询
orders = order_dao.get_by_order_no('ORD20241212001')

# 根据合约查询
xauusd_orders = order_dao.get_by_symbol('XAUUSD')

# 复合条件搜索
search_filters = {
    'symbol': 'XAUUSD',
    'side': SideType.BUY,
    'effect_type': EffectType.OPEN
}
search_results = order_dao.search_orders(search_filters)
```

### 4. 统计分析

```python
# 获取合约统计信息
stats = order_dao.get_statistics_by_symbol('XAUUSD')
print(f"总订单数: {stats['total_count']}")
print(f"总成交量: {stats['total_volume']}")
print(f"平均价格: {stats['avg_price']:.2f}")
```

## 运行示例

```bash
# 运行完整示例
python examples/order_example.py
```

## 主要特性

### 1. 数据完整性
- 成交号唯一约束
- 必填字段验证
- 数据类型检查

### 2. 查询性能
- 自动创建索引
- 优化的查询方法
- 分页支持

### 3. 统计分析
- 按合约统计
- 买卖方向统计
- 价格和成交量分析

### 4. 扩展性
- 基于SQLAlchemy ORM
- 支持多种数据库
- 易于扩展和维护

## 注意事项

1. **成交号唯一性**: `execution_id` 字段必须唯一
2. **精度处理**: 价格和数量使用 `DECIMAL(18,8)` 确保精度
3. **枚举值**: 使用枚举类型确保数据一致性
4. **会话管理**: 更新操作时注意SQLAlchemy会话管理

## 技术栈

- **数据库**: SQLite
- **ORM**: SQLAlchemy
- **语言**: Python 3.13+
- **架构**: DAO模式

## 测试验证

所有功能已通过完整测试：
- ✅ 模型创建和验证
- ✅ 数据库表创建
- ✅ CRUD操作
- ✅ 复合查询
- ✅ 统计分析
- ✅ 批量操作
- ✅ 数据转换

## 文档

- [详细使用指南](docs/order_model_usage.md)
- [代码示例](examples/order_example.py)

---

**创建时间**: 2024-12-12  
**版本**: 1.0.0  
**状态**: ✅ 完成并测试通过
