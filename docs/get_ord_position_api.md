# get_ord_position API 文档

## 概述

`get_ord_position` 函数是根据API文档 `html/fx/pos.html#pos.get_ord_position` 实现的持仓查询接口，用于获取量化交易系统中的逐笔持仓信息。该函数支持按开仓订单ID或合约代码查询持仓，如果不传参数则返回所有未平仓持仓。

## 函数签名

```python
def get_ord_position(order_id: Optional[Union[str, int]] = None, 
                    symbol: Optional[str] = None) -> List[Dict[str, Any]]
```

## 参数说明

| 参数名 | 类型 | 必选 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `order_id` | `Optional[Union[str, int]]` | 否 | `None` | 开仓订单ID，可以是字符串或整数类型 |
| `symbol` | `Optional[str]` | 否 | `None` | 合约代码，如 'EURUSD', 'XAUUSD' 等 |

### 参数详细说明

- **order_id**: 开仓订单的唯一标识符
  - 当提供此参数时，返回指定订单对应的持仓信息
  - 支持字符串和整数两种类型
  - 示例: `"216868121676222464"` 或 `216868121676222464`

- **symbol**: 金融合约的代码
  - 当提供此参数时，返回指定合约的所有持仓信息
  - 必须是字符串类型
  - 示例: `"EURUSDSP"`, `"XAUUSD"`, `"GBPUSD"`

### 参数优先级

当同时传入 `order_id` 和 `symbol` 时，优先使用 `order_id` 进行查询。

## 返回值

返回类型: `List[Dict[str, Any]]`

返回持仓信息列表，每个元素包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | `int` | 唯一编号，持仓的数据库主键ID |
| `symbol` | `str` | 合约代码 |
| `frozenQuantity` | `float` | 冻结量，当前被冻结无法交易的持仓数量 |
| `quantity` | `float` | 总持仓量，当前持仓的总数量 |
| `quantityTd` | `float` | 今日持仓量，今日新开的持仓数量 |
| `posSide` | `int` | 头寸方向，0-中性，1-多头，2-空头 |
| `profit` | `float` | 损益，总盈亏金额（已实现 + 未实现 - 费用） |
| `value` | `float` | 估值，持仓的当前市值 |
| `costPrice` | `float` | 敞口价格，开仓的平均价格 |
| `unRealizedPL` | `float` | 未交割浮动损益，未实现盈亏 |
| `realizedPL` | `float` | 已交割损益，已实现盈亏 |
| `washAmount` | `float` | 持仓成本，开仓时的总成本 |
| `time` | `int` | 头寸时间，开仓时间的时间戳（毫秒） |

### 枚举值说明

**posSide (头寸方向)**:
- `0`: 中性（无持仓）
- `1`: 多方向（多头持仓）
- `2`: 空方向（空头持仓）

## 使用示例

### 1. 获取所有未平仓持仓

```python
from custom_api.custom_api.pos.get_ord_position import get_ord_position

# 获取所有未平仓持仓
positions = get_ord_position()
print(f"共有 {len(positions)} 个未平仓持仓")

for pos in positions:
    side_text = {0: '中性', 1: '多头', 2: '空头'}[pos['posSide']]
    print(f"合约: {pos['symbol']}, 方向: {side_text}, 数量: {pos['quantity']}, 盈亏: {pos['profit']:.2f}")
```

### 2. 根据订单ID查询持仓

```python
# 根据订单ID查询持仓
order_id = "216868121676222464"
positions = get_ord_position(order_id=order_id)

if positions:
    pos = positions[0]
    print(f"订单 {order_id} 对应的持仓:")
    print(f"  合约: {pos['symbol']}")
    print(f"  持仓量: {pos['quantity']}")
    print(f"  开仓价: {pos['costPrice']}")
    print(f"  当前盈亏: {pos['profit']:.2f}")
else:
    print(f"未找到订单 {order_id} 对应的持仓")
```

### 3. 根据合约代码查询持仓

```python
# 根据合约代码查询持仓
symbol = "EURUSDSP"
positions = get_ord_position(symbol=symbol)

print(f"{symbol} 合约的持仓情况:")
for pos in positions:
    side_text = '多头' if pos['posSide'] == 1 else '空头' if pos['posSide'] == 2 else '中性'
    print(f"  ID: {pos['id']}, {side_text}, 数量: {pos['quantity']}, 盈亏: {pos['profit']:.2f}")
```

### 4. 时间戳转换

```python
from datetime import datetime

positions = get_ord_position()
if positions:
    pos = positions[0]
    # 将毫秒时间戳转换为datetime对象
    open_time = datetime.fromtimestamp(pos['time'] / 1000)
    print(f"开仓时间: {open_time.strftime('%Y-%m-%d %H:%M:%S')}")
```

### 5. 风险统计示例

```python
# 获取所有持仓并进行统计
positions = get_ord_position()

total_positions = len(positions)
total_profit = sum(pos['profit'] for pos in positions)
profitable_count = sum(1 for pos in positions if pos['profit'] > 0)

print(f"持仓统计:")
print(f"  总持仓数: {total_positions}")
print(f"  总盈亏: {total_profit:.2f}")
print(f"  盈利持仓数: {profitable_count}")
print(f"  胜率: {profitable_count/total_positions*100:.1f}%" if total_positions > 0 else "  胜率: 0%")
```

## 错误处理

### 参数类型错误

```python
try:
    # 错误的参数类型
    positions = get_ord_position(order_id=[])  # 应该是str或int
except ValueError as e:
    print(f"参数错误: {e}")
```

### 查询异常处理

```python
try:
    positions = get_ord_position(symbol="XAUUSD")
    # 处理查询结果
except Exception as e:
    print(f"查询失败: {e}")
```

## 注意事项

1. **参数优先级**: 当同时传入 `order_id` 和 `symbol` 时，优先使用 `order_id` 进行查询

2. **时间戳格式**: 返回的 `time` 字段为毫秒级时间戳，需要除以1000转换为秒级时间戳

3. **活跃持仓**: 只返回活跃状态的持仓（`is_active=True`）

4. **空结果**: 如果查询结果为空，返回空列表 `[]`，不会抛出异常

5. **数据类型**: 所有数值字段都转换为 `float` 类型，确保JSON序列化兼容性

6. **枚举映射**: `posSide` 字段与数据库中的 `PositionSide` 枚举对应：
   - `PositionSide.NEUTRAL` → `0`
   - `PositionSide.LONG` → `1`
   - `PositionSide.SHORT` → `2`

## 性能考虑

- 查询所有持仓时，函数会自动应用分页限制
- 建议在高频调用场景中添加适当的缓存机制
- 大量持仓查询时，考虑使用更具体的查询条件

## 与API文档的兼容性

该实现完全兼容原始API文档 `html/fx/pos.html#pos.get_ord_position` 的规范：

- 函数签名与文档一致
- 返回字段格式与文档示例匹配
- 支持所有文档中描述的查询方式
- 错误处理符合API规范

## 相关文档

- [持仓表模型使用指南](position_model_usage.md)
- [持仓系统总体设计](../README_POSITION_MODEL.md)
- [API使用示例](../examples/get_ord_position_example.py)
