# 持仓表模型使用指南

## 概述

持仓表（`position`）用于记录量化交易系统中的持仓信息，包括持仓状态、数量、价格、盈亏计算等详细信息。该模型与订单表（`order`）形成完整的交易记录体系。

## 表结构

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| `id` | INTEGER | - | NOT NULL | AUTO_INCREMENT | 主键，自增ID |
| `position_id` | VARCHAR | 64 | NOT NULL | - | 持仓编号，唯一标识 |
| `open_order_id` | VARCHAR | 64 | NOT NULL | - | 开仓订单ID |
| `channel_code` | VARCHAR | 32 | NOT NULL | - | 渠道编号 |
| `symbol` | VARCHAR | 32 | NOT NULL | - | 合约代码 |
| `position_side` | ENUM | - | NOT NULL | - | 持仓方向 |
| `position_status` | ENUM | - | NOT NULL | OPEN | 持仓状态 |
| `position_type` | ENUM | - | NOT NULL | NORMAL | 持仓类型 |
| `open_time` | DATETIME | - | NOT NULL | - | 开仓时间 |
| `open_price` | DECIMAL | 18,8 | NOT NULL | - | 开仓价格 |
| `current_price` | DECIMAL | 18,8 | NULL | - | 当前价格 |
| `total_quantity` | DECIMAL | 18,8 | NOT NULL | - | 总持仓量 |
| `available_quantity` | DECIMAL | 18,8 | NOT NULL | - | 可用持仓量 |
| `frozen_quantity` | DECIMAL | 18,8 | NOT NULL | 0.00 | 冻结持仓量 |
| `today_quantity` | DECIMAL | 18,8 | NOT NULL | 0.00 | 今日持仓量 |
| `position_cost` | DECIMAL | 18,8 | NOT NULL | - | 持仓成本 |
| `market_value` | DECIMAL | 18,8 | NULL | - | 持仓市值 |
| `unrealized_pnl` | DECIMAL | 18,8 | NULL | 0.00 | 未实现盈亏 |
| `realized_pnl` | DECIMAL | 18,8 | NOT NULL | 0.00 | 已实现盈亏 |
| `total_pnl` | DECIMAL | 18,8 | NULL | 0.00 | 总盈亏 |
| `commission` | DECIMAL | 18,8 | NOT NULL | 0.00 | 手续费 |
| `swap_fee` | DECIMAL | 18,8 | NOT NULL | 0.00 | 隔夜利息 |
| `stop_loss_price` | DECIMAL | 18,8 | NULL | - | 止损价格 |
| `take_profit_price` | DECIMAL | 18,8 | NULL | - | 止盈价格 |
| `close_time` | DATETIME | - | NULL | - | 平仓时间 |
| `close_price` | DECIMAL | 18,8 | NULL | - | 平仓价格 |
| `close_order_id` | VARCHAR | 64 | NULL | - | 平仓订单ID |
| `is_active` | BOOLEAN | - | NOT NULL | TRUE | 是否活跃持仓 |
| `create_time` | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录创建时间 |
| `update_time` | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录更新时间 |
| `remark` | TEXT | - | NULL | - | 备注信息 |

## 枚举类型

### PositionSide (持仓方向)
- `NEUTRAL`: 中性（无持仓）
- `LONG`: 多头持仓
- `SHORT`: 空头持仓

### PositionStatus (持仓状态)
- `OPEN`: 持仓中
- `CLOSED`: 已平仓
- `PARTIAL`: 部分平仓

### PositionType (持仓类型)
- `NORMAL`: 普通持仓
- `HEDGE`: 对冲持仓
- `ARBITRAGE`: 套利持仓

## 使用示例

### 1. 导入模块

```python
from custom_api.models import Position, PositionSide, PositionStatus, PositionType
from custom_api.dao import get_position_dao
from custom_api.database.connection import init_database, get_db_session
from datetime import datetime
from decimal import Decimal
```

### 2. 初始化数据库

```python
# 初始化数据库（创建表）
init_database()
```

### 3. 创建持仓记录

```python
# 获取DAO实例
position_dao = get_position_dao()

# 创建持仓数据
position_data = {
    'position_id': 'POS20241212001',
    'open_order_id': 'ORD20241212001',
    'channel_code': 'CHANNEL001',
    'symbol': 'XAUUSD',
    'position_side': PositionSide.LONG,
    'position_status': PositionStatus.OPEN,
    'position_type': PositionType.NORMAL,
    'open_time': datetime.now(),
    'open_price': Decimal('2650.50'),
    'current_price': Decimal('2655.80'),
    'total_quantity': Decimal('1.0'),
    'available_quantity': Decimal('1.0'),
    'frozen_quantity': Decimal('0.0'),
    'today_quantity': Decimal('1.0'),
    'position_cost': Decimal('2650.50'),
    'commission': Decimal('5.30'),
    'swap_fee': Decimal('0.50'),
    'stop_loss_price': Decimal('2620.00'),
    'take_profit_price': Decimal('2680.00'),
    'is_active': True,
    'remark': '黄金多头持仓'
}

# 创建持仓
position = position_dao.create(position_data)
print(f"创建持仓成功，ID: {position.id}")
```

### 4. 查询持仓

```python
# 根据ID查询
position = position_dao.get(1)

# 根据持仓编号查询
position = position_dao.get_by_position_id('POS20241212001')

# 根据开仓订单ID查询
position = position_dao.get_by_open_order_id('ORD20241212001')

# 根据合约查询
xauusd_positions = position_dao.get_by_symbol('XAUUSD')

# 根据渠道查询
channel_positions = position_dao.get_by_channel('CHANNEL001')

# 根据持仓方向查询
long_positions = position_dao.get_by_side(PositionSide.LONG)

# 获取活跃持仓
active_positions = position_dao.get_active_positions()

# 获取开仓状态持仓
open_positions = position_dao.get_open_positions()
```

### 5. 盈亏查询

```python
# 获取盈利持仓
profitable_positions = position_dao.get_profitable_positions()

# 获取亏损持仓
losing_positions = position_dao.get_losing_positions()
```

### 6. 价格更新和盈亏计算

```python
# 批量更新价格
price_updates = [
    {'symbol': 'XAUUSD', 'price': 2665.00},
    {'symbol': 'EURUSD', 'price': 1.0535}
]
updated_count = position_dao.update_current_prices(price_updates)

# 单个持仓价格更新
position.update_current_price(Decimal('2665.00'))

# 计算盈亏
unrealized_pnl = position.calculate_unrealized_pnl()
total_pnl = position.calculate_total_pnl()
return_rate = position.get_return_rate()
is_profitable = position.is_profitable()
```

### 7. 统计分析

```python
# 获取指定合约的统计信息
stats = position_dao.get_statistics_by_symbol('XAUUSD')
print(f"总持仓数: {stats['total_count']}")
print(f"活跃持仓数: {stats['active_count']}")
print(f"多头持仓数: {stats['long_count']}")
print(f"空头持仓数: {stats['short_count']}")
print(f"总持仓量: {stats['total_quantity']}")
print(f"总成本: {stats['total_cost']}")
print(f"总盈亏: {stats['total_pnl']}")

# 获取整体统计信息
overall_stats = position_dao.get_overall_statistics()
print(f"胜率: {overall_stats['win_rate']:.2f}%")
print(f"总收益率: {overall_stats['total_return_rate']:.2f}%")
```

### 8. 复合条件搜索

```python
# 复合搜索条件
search_filters = {
    'symbol': 'XAUUSD',
    'position_side': PositionSide.LONG,
    'position_status': PositionStatus.OPEN,
    'is_active': True,
    'start_date': datetime.now() - timedelta(days=7),
    'end_date': datetime.now(),
    'min_pnl': 0  # 只查询盈利的持仓
}

# 执行搜索
search_results = position_dao.search_positions(
    filters=search_filters,
    skip=0,
    limit=50,
    order_by='open_time',
    desc_order=True
)
```

### 9. 平仓操作

```python
# 平仓持仓
closed_position = position_dao.close_position(
    position_id='POS20241212001',
    close_price=Decimal('2670.00'),
    close_order_id='CLOSE20241212001'
)

if closed_position:
    print(f"平仓成功: {closed_position.symbol}")
    print(f"已实现盈亏: {closed_position.realized_pnl}")
```

### 10. 数据转换

```python
# 转换为字典
position_dict = position.to_dict()

# 从字典创建持仓
new_position_data = {
    'position_id': 'POS20241212002',
    'open_order_id': 'ORD20241212002',
    'channel_code': 'CHANNEL001',
    'symbol': 'EURUSD',
    'position_side': 'SHORT',  # 字符串形式
    'position_status': 'OPEN',  # 字符串形式
    'position_type': 'NORMAL',  # 字符串形式
    'open_time': datetime.now(),
    'open_price': Decimal('1.0550'),
    'total_quantity': Decimal('10000.0'),
    'available_quantity': Decimal('10000.0'),
    'position_cost': Decimal('10550.00')
}

new_position = Position.from_dict(new_position_data)
```

## 与订单表的关联

持仓表与订单表通过以下字段关联：
- `open_order_id`: 关联开仓订单
- `close_order_id`: 关联平仓订单

可以通过这些字段追踪持仓的完整生命周期。

## 业务逻辑

### 持仓生命周期
1. **开仓**: 创建持仓记录，状态为 `OPEN`
2. **价格更新**: 定期更新 `current_price` 和相关盈亏
3. **部分平仓**: 状态变为 `PARTIAL`，减少持仓量
4. **完全平仓**: 状态变为 `CLOSED`，`is_active` 设为 `False`

### 盈亏计算
- **未实现盈亏**: (当前价格 - 开仓价格) × 持仓量 × 方向系数
- **总盈亏**: 已实现盈亏 + 未实现盈亏 - 手续费 - 隔夜利息
- **收益率**: 总盈亏 / 持仓成本 × 100%

## 注意事项

1. **持仓编号唯一性**: `position_id` 字段必须唯一
2. **精度处理**: 价格和数量字段使用 `DECIMAL(18,8)` 类型
3. **枚举值**: 使用枚举类型确保数据一致性
4. **价格更新**: 定期更新当前价格以获得准确的盈亏信息
5. **会话管理**: 更新操作时注意SQLAlchemy会话管理

## 索引优化

系统自动为以下字段创建索引：
- `position_id`: 持仓编号唯一索引
- `open_order_id`: 开仓订单ID索引
- `channel_code`: 渠道编号索引
- `symbol`: 合约代码索引
- `position_side`: 持仓方向索引
- `position_status`: 持仓状态索引
- `open_time`: 开仓时间索引
- `is_active`: 活跃状态索引

这些索引可以显著提高查询性能。
