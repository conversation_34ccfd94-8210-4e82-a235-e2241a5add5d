# 订单表模型使用指南

## 概述

订单表（`order`）用于记录量化交易系统中的订单成交信息，包括成交价格、成交量、手续费等详细信息。

## 表结构

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| `id` | INTEGER | - | NOT NULL | AUTO_INCREMENT | 主键，自增ID |
| `order_no` | VARCHAR | 64 | NOT NULL | - | 订单号，关联原始订单 |
| `execution_id` | VARCHAR | 64 | NOT NULL | - | 成交号，唯一标识每笔成交 |
| `execution_time` | DATETIME | - | NOT NULL | - | 成交时间 |
| `channel_code` | VARCHAR | 32 | NOT NULL | - | 渠道编号 |
| `symbol` | VARCHAR | 32 | NOT NULL | - | 合约代码 |
| `side` | ENUM | - | NOT NULL | - | 成交方向 (BUY/SELL) |
| `execution_price` | DECIMAL | 18,8 | NOT NULL | - | 成交价格 |
| `execution_quantity` | DECIMAL | 18,8 | NOT NULL | - | 成交量 |
| `effect_type` | ENUM | - | NOT NULL | - | 开平仓类型 |
| `order_type` | ENUM | - | NOT NULL | - | 订单类型 |
| `value_date` | VARCHAR | 8 | NULL | - | 近端交割日 (yyyyMMdd) |
| `maturity_date` | VARCHAR | 8 | NULL | - | 远端交割日 (yyyyMMdd) |
| `commission` | DECIMAL | 18,8 | NULL | 0.00 | 手续费 |
| `create_time` | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录创建时间 |
| `update_time` | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 记录更新时间 |
| `remark` | TEXT | - | NULL | - | 备注信息 |

## 枚举类型

### SideType (成交方向)
- `BUY`: 买入
- `SELL`: 卖出

### EffectType (开平仓类型)
- `OPEN`: 开仓
- `CLOSE`: 平仓
- `CLOSE_TODAY`: 平今
- `CLOSE_YESTERDAY`: 平昨

### OrderType (订单类型)
- `MARKET`: 市价单
- `LIMIT`: 限价单
- `STOP`: 止损单
- `STOP_LIMIT`: 止损限价单
- `IOC`: 立即成交或取消
- `FOK`: 全部成交或取消
- `GTC`: 撤销前有效
- `GTD`: 指定日期前有效

## 使用示例

### 1. 导入模块

```python
from custom_api.models import Order, SideType, EffectType, OrderType
from custom_api.dao import get_order_dao
from custom_api.database.connection import init_database, get_db_session
from datetime import datetime
from decimal import Decimal
```

### 2. 初始化数据库

```python
# 初始化数据库（创建表）
init_database()
```

### 3. 创建订单记录

```python
# 获取DAO实例
order_dao = get_order_dao()

# 创建订单数据
order_data = {
    'order_no': 'ORD20241212001',
    'execution_id': 'EXE20241212001001',
    'execution_time': datetime.now(),
    'channel_code': 'CHANNEL001',
    'symbol': 'XAUUSD',
    'side': SideType.BUY,
    'execution_price': Decimal('2650.50'),
    'execution_quantity': Decimal('1.0'),
    'effect_type': EffectType.OPEN,
    'order_type': OrderType.MARKET,
    'commission': Decimal('5.30'),
    'remark': '黄金买入开仓'
}

# 创建订单
order = order_dao.create(order_data)
print(f"创建订单成功，ID: {order.id}")
```

### 4. 查询订单

```python
# 根据ID查询
order = order_dao.get(1)

# 根据成交号查询
order = order_dao.get_by_execution_id('EXE20241212001001')

# 根据订单号查询（可能有多个成交）
orders = order_dao.get_by_order_no('ORD20241212001')

# 根据合约查询
xauusd_orders = order_dao.get_by_symbol('XAUUSD')

# 根据渠道查询
channel_orders = order_dao.get_by_channel('CHANNEL001')

# 根据成交方向查询
buy_orders = order_dao.get_by_side(SideType.BUY)
```

### 5. 日期和价格范围查询

```python
from datetime import datetime, timedelta

# 查询最近24小时的订单
start_date = datetime.now() - timedelta(days=1)
end_date = datetime.now()
recent_orders = order_dao.get_by_date_range(start_date, end_date)

# 查询价格范围内的订单
min_price = Decimal('2600.00')
max_price = Decimal('2700.00')
price_range_orders = order_dao.get_by_price_range(min_price, max_price)
```

### 6. 复合条件搜索

```python
# 复合搜索条件
search_filters = {
    'symbol': 'XAUUSD',
    'side': SideType.BUY,
    'effect_type': EffectType.OPEN,
    'start_date': datetime.now() - timedelta(days=7),
    'end_date': datetime.now(),
    'min_price': Decimal('2600.00'),
    'max_price': Decimal('2700.00')
}

# 执行搜索
search_results = order_dao.search_orders(
    filters=search_filters,
    skip=0,
    limit=50,
    order_by='execution_time',
    desc_order=True
)
```

### 7. 统计信息

```python
# 获取指定合约的统计信息
stats = order_dao.get_statistics_by_symbol('XAUUSD')
print(f"总订单数: {stats['total_count']}")
print(f"买入订单数: {stats['buy_count']}")
print(f"卖出订单数: {stats['sell_count']}")
print(f"总成交量: {stats['total_volume']}")
print(f"总手续费: {stats['total_commission']}")
print(f"平均价格: {stats['avg_price']:.2f}")
```

### 8. 批量操作

```python
# 批量创建订单
orders_data = [
    {
        'order_no': 'ORD20241212002',
        'execution_id': 'EXE20241212002001',
        'execution_time': datetime.now(),
        'channel_code': 'CHANNEL001',
        'symbol': 'EURUSD',
        'side': SideType.BUY,
        'execution_price': Decimal('1.0550'),
        'execution_quantity': Decimal('10000.0'),
        'effect_type': EffectType.OPEN,
        'order_type': OrderType.LIMIT,
        'commission': Decimal('2.10')
    },
    # ... 更多订单数据
]

# 批量创建
created_orders = order_dao.batch_create(orders_data)
print(f"批量创建了 {len(created_orders)} 个订单")
```

### 9. 更新订单

```python
# 使用会话进行更新
with get_db_session() as session:
    # 查询要更新的订单
    order = session.query(Order).filter_by(id=1).first()
    
    if order:
        # 更新数据
        update_data = {
            'remark': '已更新的备注信息',
            'commission': Decimal('6.00')
        }
        
        # 执行更新
        updated_order = order_dao.update(order, update_data, session)
        print(f"更新成功: {updated_order.remark}")
```

### 10. 数据转换

```python
# 转换为字典
order_dict = order.to_dict()

# 从字典创建订单
new_order_data = {
    'order_no': 'ORD20241212003',
    'execution_id': 'EXE20241212003001',
    'execution_time': datetime.now(),
    'channel_code': 'CHANNEL001',
    'symbol': 'GBPUSD',
    'side': 'SELL',  # 字符串形式
    'execution_price': Decimal('1.2750'),
    'execution_quantity': Decimal('5000.0'),
    'effect_type': 'OPEN',  # 字符串形式
    'order_type': 'MARKET',  # 字符串形式
    'commission': Decimal('1.25')
}

new_order = Order.from_dict(new_order_data)
```

## 注意事项

1. **成交号唯一性**: `execution_id` 字段必须唯一，每笔成交都有唯一的成交号
2. **订单号关联**: 同一个 `order_no` 可能对应多个成交记录
3. **精度处理**: 价格和数量字段使用 `DECIMAL(18,8)` 类型，确保精度
4. **枚举值**: 使用枚举类型确保数据一致性
5. **时间字段**: `execution_time` 记录实际成交时间，`create_time` 和 `update_time` 记录数据库操作时间
6. **会话管理**: 更新操作时注意会话管理，确保对象在正确的会话中

## 索引优化

系统自动为以下字段创建索引：
- `order_no`: 订单号索引
- `execution_id`: 成交号唯一索引
- `execution_time`: 成交时间索引
- `channel_code`: 渠道编号索引
- `symbol`: 合约代码索引

这些索引可以显著提高查询性能。
