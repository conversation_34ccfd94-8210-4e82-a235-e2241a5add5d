"""
订单服务层

提供订单相关的业务逻辑和高级操作
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..database import get_db_session
from ..dao import get_order_dao
from ..models.order import Order, OrderStatus, OrderDirection
from ..to_order import to_order

logger = logging.getLogger(__name__)


class OrderService:
    """订单服务类"""
    
    def __init__(self):
        self.dao = get_order_dao()
    
    def create_order_from_to_order_params(self, **kwargs) -> Optional[str]:
        """
        从 to_order 参数创建订单并提交
        
        Args:
            **kwargs: to_order 函数参数
            
        Returns:
            订单号或None
        """
        try:
            with get_db_session() as session:
                # 生成订单号
                import uuid
                order_no = f"ORD{datetime.now().strftime('%Y%m%d%H%M%S')}{str(uuid.uuid4())[:8]}"
                
                # 创建订单记录
                order_data = {
                    'order_no': order_no,
                    'symbol': kwargs.get('symbol'),
                    'side': OrderDirection.BUY if kwargs.get('side') == 'B' else OrderDirection.SELL,
                    'order_price': kwargs.get('price'),
                    'quantity': kwargs.get('quantity'),
                    'channel_code': kwargs.get('channel_code', 'DEFAULT'),
                    'order_type': 'limit' if kwargs.get('order_type', 2) == 2 else 'market',
                    'time_in_force': kwargs.get('time_in_force'),
                    'open_close_type': 'open' if kwargs.get('effect', 1) == 1 else 'close',
                    'stop_loss_price': kwargs.get('stop_loss_price'),
                    'take_profit_price': kwargs.get('take_profit_price'),
                    'value_date': kwargs.get('value_date'),
                    'maturity_date': kwargs.get('maturity_date'),
                    'status': OrderStatus.PENDING
                }
                
                order = self.dao.create(order_data, session)
                logger.info(f"创建订单记录成功: {order.order_no}")
                
                try:
                    # 调用 to_order 函数提交订单
                    external_order_id = to_order(**kwargs)
                    
                    if external_order_id and external_order_id != '0':
                        # 订单提交成功
                        logger.info(f"订单提交成功: {order.order_no} -> {external_order_id}")
                        return order.order_no
                    else:
                        # 订单提交失败
                        order.status = OrderStatus.REJECTED
                        order.remark = "订单提交失败"
                        session.commit()
                        logger.warning(f"订单提交失败: {order.order_no}")
                        return None
                        
                except Exception as e:
                    # 订单提交异常
                    order.status = OrderStatus.REJECTED
                    order.remark = str(e)
                    session.commit()
                    logger.error(f"订单提交异常: {order.order_no}, 错误: {e}")
                    return None
                    
        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            return None
    
    def get_order_by_no(self, order_no: str) -> Optional[Order]:
        """
        根据订单号获取订单
        
        Args:
            order_no: 订单号
            
        Returns:
            订单实例或None
        """
        return self.dao.get_by_order_no(order_no)
    
    def get_orders_by_symbol(self, symbol: str, status: Optional[OrderStatus] = None,
                            page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        根据合约获取订单列表（分页）
        
        Args:
            symbol: 合约代码
            status: 订单状态（可选）
            page: 页码（从1开始）
            page_size: 每页大小
            
        Returns:
            包含订单列表和分页信息的字典
        """
        skip = (page - 1) * page_size
        orders = self.dao.get_by_symbol(symbol, status, skip, page_size)
        
        # 获取总数（用于分页）
        with get_db_session() as session:
            total_query = session.query(Order).filter(Order.symbol == symbol)
            if status:
                total_query = total_query.filter(Order.status == status)
            total_count = total_query.count()
        
        return {
            'orders': [order.to_dict() for order in orders],
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        }
    
    def get_pending_orders(self, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """
        获取待处理订单列表
        
        Args:
            page: 页码
            page_size: 每页大小
            
        Returns:
            包含订单列表和分页信息的字典
        """
        skip = (page - 1) * page_size
        orders = self.dao.get_by_status(OrderStatus.PENDING, skip, page_size)
        
        with get_db_session() as session:
            total_count = session.query(Order).filter(
                Order.status == OrderStatus.PENDING
            ).count()
        
        return {
            'orders': [order.to_dict() for order in orders],
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_pages': (total_count + page_size - 1) // page_size
            }
        }
    
    def update_order_status(self, order_no: str, status: OrderStatus,
                           filled_quantity: Optional[float] = None) -> bool:
        """
        更新订单状态
        
        Args:
            order_no: 订单号
            status: 新状态
            filled_quantity: 成交数量
            
        Returns:
            是否更新成功
        """
        return self.dao.update_order_status(order_no, status, filled_quantity)
    
    def cancel_order(self, order_no: str, reason: str = "用户取消") -> bool:
        """
        取消订单
        
        Args:
            order_no: 订单号
            reason: 取消原因
            
        Returns:
            是否取消成功
        """
        order = self.dao.get_by_order_no(order_no)
        if not order:
            logger.warning(f"订单不存在: {order_no}")
            return False
        
        if order.status not in [OrderStatus.PENDING, OrderStatus.PARTIAL_FILLED]:
            logger.warning(f"订单状态不允许取消: {order_no}, 状态: {order.status}")
            return False
        
        # 这里可以添加调用外部系统取消订单的逻辑
        # cancel_external_order(order.external_order_id)
        
        return self.dao.update_order_status(order_no, OrderStatus.CANCELLED, None)
    
    def get_order_statistics(self, symbol: str) -> Dict[str, Any]:
        """
        获取订单统计信息
        
        Args:
            symbol: 合约代码
            
        Returns:
            统计信息字典
        """
        return self.dao.get_statistics_by_symbol(symbol)
    
    def get_recent_orders(self, hours: int = 24, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取最近的订单
        
        Args:
            hours: 小时数
            limit: 限制数量
            
        Returns:
            订单列表
        """
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        orders = self.dao.get_by_date_range(start_time, end_time, 0, limit)
        return [order.to_dict() for order in orders]


# 全局服务实例
order_service = OrderService()


def get_order_service() -> OrderService:
    """获取订单服务实例"""
    return order_service
