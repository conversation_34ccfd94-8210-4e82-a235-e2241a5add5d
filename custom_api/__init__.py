"""
custom_api 包

提供量化交易相关的API模拟实现，包括：
- qlog: 日志功能模块
- md: 市场数据模块
- deal: 交易执行模块
- base: 基础功能模块
- pos: 持仓管理模块
"""

# 导入主要模块
from . import qlog
from . import param
from . import md
from . import deal
from . import base
from . import pos
from . import context

# 版本信息
__version__ = "1.0.0"
__author__ = "AI Assistant"
__description__ = "量化交易API的模拟实现"

# 导出的模块列表
__all__ = [
    'qlog',
    'param',
    'md',
    'deal',
    'base',
    'pos',
    'context'
]