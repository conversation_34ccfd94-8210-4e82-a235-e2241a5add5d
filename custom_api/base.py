from typing import Dict, Optional
from datetime import datetime, timedelta
import random


def get_contract(code: str) -> Optional[Dict]:
    """
    获取合约信息 - Mock 实现

    Args:
        code (str): 合约唯一代码，合约名

    Returns:
        dict: 合约信息字典，包含以下字段：
            - code(string): 合约代码
            - contractType(string): 合约类型 ContractTypeEnum {B:基础合约，D:基差合约,T:期差合约,S:连续合约,M:月份合约,N:非标准合约}
            - sites(string): 交易主体
            - productBroad(string): 产品大类
            - products(string): 产品小类
            - contractMultiplier(float): 合约乘数
            - tenor(string): 期限
            - tenorGroup(string): 组合期限
            - lastDate(string): 最后交易日
            - dealTypeGroup(string): 组合交易品种
            - dealType(string): 交易品种
            - valueDateRule(string): 起息日规则
            - market(string): 交易市场
            - quoteCurrency(string): 报价货币
            - localName(string): 本地名称
            - name(string): 英文名称
            - quoteUnit(string): 报价单位
            - startDate(string): 开始交易日
            - noDecimal(int): 报价有效位数
            - status(string): 合约状态

        如果合约不存在，返回 None
    """

    return {
        "noDecimal": 4
    }
