"""
qlog 模块 - 日志功能的模拟实现

这是一个模拟版本的 qlog 模块，提供与原始 quantapi.qlog 相同的接口和方法。
主要用于测试环境中替代原始的 qlog 模块。

功能特点：
1. 提供 info, info_f, error, error_f 四个主要方法
2. 支持格式化字符串输出
3. 带有时间戳和日志级别标识
4. 彩色输出支持（可选）
5. 文件日志输出支持（可选）

作者: AI Assistant
创建时间: 2025-08-12
"""

import logging
import sys
import os
from datetime import datetime
from typing import Any, Optional
import threading

# 线程锁，确保日志输出的线程安全
_log_lock = threading.Lock()

# 全局配置
class QLogConfig:
    """qlog 配置类"""
    def __init__(self):
        self.enable_color = True  # 是否启用彩色输出
        self.enable_file_log = False  # 是否启用文件日志
        self.log_file_path = "qlog.log"  # 日志文件路径
        self.date_format = "%Y-%m-%d %H:%M:%S"  # 时间格式
        self.enable_console = True  # 是否启用控制台输出
        
    def set_file_logging(self, enabled: bool, file_path: str = "qlog.log"):
        """设置文件日志"""
        self.enable_file_log = enabled
        self.log_file_path = file_path
        
    def set_color_output(self, enabled: bool):
        """设置彩色输出"""
        self.enable_color = enabled
        
    def set_console_output(self, enabled: bool):
        """设置控制台输出"""
        self.enable_console = enabled

# 全局配置实例
_config = QLogConfig()

# ANSI 颜色代码
class Colors:
    """ANSI 颜色代码"""
    RESET = '\033[0m'
    BOLD = '\033[1m'
    
    # 前景色
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'
    
    # 亮色
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'

def _get_timestamp() -> str:
    """获取当前时间戳字符串"""
    return datetime.now().strftime(_config.date_format)

def _colorize_text(text: str, color: str) -> str:
    """给文本添加颜色"""
    if not _config.enable_color:
        return text
    return f"{color}{text}{Colors.RESET}"

def _write_to_file(message: str):
    """写入文件日志"""
    if not _config.enable_file_log:
        return
        
    try:
        # 确保日志目录存在
        log_dir = os.path.dirname(_config.log_file_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        with open(_config.log_file_path, 'a', encoding='utf-8') as f:
            f.write(message + '\n')
            f.flush()
    except Exception as e:
        # 文件写入失败时，输出到控制台
        print(f"[qlog] 文件日志写入失败: {e}", file=sys.stderr)

def _log_message(level: str, message: str, color: str):
    """统一的日志输出方法"""
    with _log_lock:
        timestamp = _get_timestamp()
        formatted_message = f"[{timestamp}] [{level}] {message}"
        
        # 控制台输出
        if _config.enable_console:
            colored_message = _colorize_text(formatted_message, color)
            print(colored_message)
            sys.stdout.flush()
        
        # 文件输出（不带颜色）
        _write_to_file(formatted_message)

def info(message: str):
    """
    打印info级别日志
    
    Args:
        message: 日志消息
        
    Example:
        >>> qlog.info("策略启动")
        [2025-08-12 10:30:45] [INFO] 策略启动
    """
    _log_message("INFO", str(message), Colors.GREEN)

def info_f(format_str: str, *args: Any):
    """
    打印info级别格式化日志，提供占位符的方式，以参数化的方式打印日志
    
    Args:
        format_str: 格式化字符串，使用 {} 作为占位符
        *args: 格式化参数
        
    Example:
        >>> version = "第一版"
        >>> qlog.info_f("{}策略启动", version)
        [2025-08-12 10:30:45] [INFO] 第一版策略启动
        
        >>> qlog.info_f("[指标信息] MA30: {:.5f}, MA240: {:.5f}", 1.23456, 2.34567)
        [2025-08-12 10:30:45] [INFO] [指标信息] MA30: 1.23456, MA240: 2.34567
    """
    try:
        message = format_str.format(*args)
        _log_message("INFO", message, Colors.GREEN)
    except Exception as e:
        # 格式化失败时，输出原始信息和错误
        error_msg = f"日志格式化失败: {format_str}, args: {args}, error: {e}"
        _log_message("ERROR", error_msg, Colors.RED)

def error(message: str):
    """
    打印error级别日志
    
    Args:
        message: 错误消息
        
    Example:
        >>> qlog.error("连接失败")
        [2025-08-12 10:30:45] [ERROR] 连接失败
    """
    _log_message("ERROR", str(message), Colors.RED)

def error_f(format_str: str, *args: Any):
    """
    打印error级别格式化日志，提供占位符的方式，以参数化的方式打印日志
    
    Args:
        format_str: 格式化字符串，使用 {} 作为占位符
        *args: 格式化参数
        
    Example:
        >>> error_code = 404
        >>> qlog.error_f("请求失败，错误代码: {}", error_code)
        [2025-08-12 10:30:45] [ERROR] 请求失败，错误代码: 404
    """
    try:
        message = format_str.format(*args)
        _log_message("ERROR", message, Colors.RED)
    except Exception as e:
        # 格式化失败时，输出原始信息和错误
        error_msg = f"错误日志格式化失败: {format_str}, args: {args}, error: {e}"
        _log_message("ERROR", error_msg, Colors.RED)

# 配置相关的公共方法
def configure(enable_color: bool = True, 
              enable_file_log: bool = False, 
              log_file_path: str = "qlog.log",
              enable_console: bool = True):
    """
    配置 qlog 模块
    
    Args:
        enable_color: 是否启用彩色输出
        enable_file_log: 是否启用文件日志
        log_file_path: 日志文件路径
        enable_console: 是否启用控制台输出
    """
    _config.enable_color = enable_color
    _config.enable_file_log = enable_file_log
    _config.log_file_path = log_file_path
    _config.enable_console = enable_console

def get_config() -> QLogConfig:
    """获取当前配置"""
    return _config

# 兼容性别名（如果原始API有其他名称）
log_info = info
log_info_f = info_f
log_error = error
log_error_f = error_f

# 模块信息
__version__ = "1.0.0"
__author__ = "AI Assistant"
__description__ = "qlog 模块的模拟实现，提供与原始 quantapi.qlog 相同的接口"

# 模块初始化时的欢迎信息（可选，用于调试）
if __name__ == "__main__":
    # 测试代码
    info("qlog 模块测试开始")
    info_f("当前版本: {}, 作者: {}", __version__, __author__)
    
    # 测试格式化功能
    version = "v1.0"
    strategy_name = "测试策略"
    info_f("[init]初始化 [{}], 版本号: {}", strategy_name, version)
    
    # 测试数值格式化
    ma_30 = 1.23456
    ma_240 = 2.34567
    macd = 0.00123
    price = 1.08945
    info_f("[指标信息] MA30: {:.5f}, MA240: {:.5f}, MACD: {:.5f}, 当前价格: {:.5f}", 
           ma_30, ma_240, macd, price)
    
    # 测试错误日志
    error("这是一个测试错误")
    error_f("错误代码: {}, 错误信息: {}", 500, "服务器内部错误")
    
    info("qlog 模块测试完成")
