"""
数据库模块

提供数据库连接、会话管理和数据访问对象
"""

from .config import (
    DatabaseConfig,
    ConfigManager,
    get_database_config,
    get_database_url,
    get_engine_kwargs
)

from .connection import (
    DatabaseManager,
    get_db_manager,
    get_engine,
    get_session,
    get_scoped_session,
    get_db_session,
    get_db_transaction,
    init_database,
    cleanup_database
)

# DAO相关导入移除，避免循环导入

__all__ = [
    # 配置相关
    "DatabaseConfig",
    "ConfigManager", 
    "get_database_config",
    "get_database_url",
    "get_engine_kwargs",
    
    # 连接管理相关
    "DatabaseManager",
    "get_db_manager",
    "get_engine",
    "get_session",
    "get_scoped_session", 
    "get_db_session",
    "get_db_transaction",
    "init_database",
    "cleanup_database",
    
    # DAO相关导入移除，避免循环导入
]
