"""
数据库连接和会话管理模块

提供数据库连接、会话管理和事务处理功能
"""

import logging
from contextlib import contextmanager
from typing import Generator, Optional, Any
from sqlalchemy import create_engine, Engine, event
from sqlalchemy.orm import sessionmaker, Session, scoped_session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import StaticPool

from .config import get_database_config, get_database_url, get_engine_kwargs

# 配置日志
logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self._engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
        self._scoped_session: Optional[scoped_session] = None
        self._initialized = False
    
    def initialize(self) -> None:
        """初始化数据库连接"""
        if self._initialized:
            logger.warning("数据库已经初始化，跳过重复初始化")
            return
        
        try:
            config = get_database_config()
            database_url = get_database_url()
            engine_kwargs = get_engine_kwargs()
            
            logger.info(f"初始化数据库连接: {config.database_type}")
            logger.debug(f"数据库URL: {database_url}")
            
            # 为 SQLite 内存数据库添加特殊配置
            if database_url.startswith("sqlite:///:memory:"):
                engine_kwargs.update({
                    "poolclass": StaticPool,
                    "connect_args": {
                        "check_same_thread": False
                    }
                })
            elif database_url.startswith("sqlite:///"):
                engine_kwargs.update({
                    "connect_args": {
                        "check_same_thread": False
                    }
                })
            
            # 创建数据库引擎
            self._engine = create_engine(database_url, **engine_kwargs)
            
            # 添加连接事件监听器
            self._setup_event_listeners()
            
            # 创建会话工厂
            self._session_factory = sessionmaker(
                bind=self._engine,
                autocommit=False,
                autoflush=False,
                expire_on_commit=False
            )
            
            # 创建线程安全的会话
            self._scoped_session = scoped_session(self._session_factory)
            
            self._initialized = True
            logger.info("数据库连接初始化成功")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _setup_event_listeners(self) -> None:
        """设置数据库事件监听器"""
        
        @event.listens_for(self._engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """为 SQLite 设置 PRAGMA"""
            if self._engine.dialect.name == "sqlite":
                cursor = dbapi_connection.cursor()
                # 启用外键约束
                cursor.execute("PRAGMA foreign_keys=ON")
                # 设置 WAL 模式以提高并发性能
                cursor.execute("PRAGMA journal_mode=WAL")
                # 设置同步模式
                cursor.execute("PRAGMA synchronous=NORMAL")
                cursor.close()
        
        @event.listens_for(self._engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """连接检出事件"""
            logger.debug("数据库连接已检出")
        
        @event.listens_for(self._engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """连接检入事件"""
            logger.debug("数据库连接已检入")
    
    @property
    def engine(self) -> Engine:
        """获取数据库引擎"""
        if not self._initialized:
            self.initialize()
        return self._engine
    
    @property
    def session_factory(self) -> sessionmaker:
        """获取会话工厂"""
        if not self._initialized:
            self.initialize()
        return self._session_factory
    
    def get_session(self) -> Session:
        """获取新的数据库会话"""
        if not self._initialized:
            self.initialize()
        return self._session_factory()
    
    def get_scoped_session(self) -> scoped_session:
        """获取线程安全的会话"""
        if not self._initialized:
            self.initialize()
        return self._scoped_session
    
    def close_all_sessions(self) -> None:
        """关闭所有会话"""
        if self._scoped_session:
            self._scoped_session.remove()
    
    def dispose(self) -> None:
        """释放数据库连接池"""
        if self._engine:
            self._engine.dispose()
            logger.info("数据库连接池已释放")
    
    def create_all_tables(self) -> None:
        """创建所有表"""
        try:
            # 导入所有模型以确保它们被注册到 Base.metadata
            from ..models.order import Base
            from ..models.order import Order  # 导入订单模型
            from ..models.trade import Trade  # 导入交易模型
            from ..models.position import Position  # 导入持仓模型
            Base.metadata.create_all(bind=self.engine)
            logger.info("数据库表创建成功")
        except Exception as e:
            logger.error(f"创建数据库表失败: {e}")
            raise
    
    def drop_all_tables(self) -> None:
        """删除所有表"""
        try:
            # 导入所有模型以确保它们被注册到 Base.metadata
            from ..models.order import Base
            from ..models.order import Order  # 导入订单模型
            from ..models.trade import Trade  # 导入交易模型
            from ..models.position import Position  # 导入持仓模型
            Base.metadata.drop_all(bind=self.engine)
            logger.info("数据库表删除成功")
        except Exception as e:
            logger.error(f"删除数据库表失败: {e}")
            raise
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            from sqlalchemy import text
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
                logger.info("数据库连接测试成功")
                return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_db_manager() -> DatabaseManager:
    """获取数据库管理器"""
    return db_manager


def get_engine() -> Engine:
    """获取数据库引擎"""
    return db_manager.engine


def get_session() -> Session:
    """获取新的数据库会话"""
    return db_manager.get_session()


def get_scoped_session() -> scoped_session:
    """获取线程安全的会话"""
    return db_manager.get_scoped_session()


@contextmanager
def get_db_session() -> Generator[Session, None, None]:
    """
    获取数据库会话的上下文管理器
    
    使用示例:
        with get_db_session() as session:
            # 执行数据库操作
            pass
    """
    session = get_session()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"数据库操作失败，已回滚: {e}")
        raise
    finally:
        session.close()


@contextmanager
def get_db_transaction() -> Generator[Session, None, None]:
    """
    获取数据库事务的上下文管理器
    
    使用示例:
        with get_db_transaction() as session:
            # 执行事务操作
            pass
    """
    session = get_session()
    transaction = session.begin()
    try:
        yield session
        transaction.commit()
    except Exception as e:
        transaction.rollback()
        logger.error(f"事务执行失败，已回滚: {e}")
        raise
    finally:
        session.close()


def init_database() -> None:
    """初始化数据库"""
    logger.info("开始初始化数据库...")
    
    # 初始化数据库管理器
    db_manager.initialize()
    
    # 创建所有表
    db_manager.create_all_tables()
    
    # 测试连接
    if db_manager.test_connection():
        logger.info("数据库初始化完成")
    else:
        raise RuntimeError("数据库初始化失败：连接测试不通过")


def cleanup_database() -> None:
    """清理数据库资源"""
    logger.info("清理数据库资源...")
    db_manager.close_all_sessions()
    db_manager.dispose()
    logger.info("数据库资源清理完成")
