from datetime import datetime
from datetime import timezone

import numpy as np
import pandas as pd

from custom_api.utils.get_market_data import fetch_history_bars_by_financego
from custom_api.utils.get_market_data import fetch_latest_price_from_bars


def query_bars_pro(symbol, type_, source, count=-1, fields=None, data_type=1, start_datetime='', end_datetime=''):
    """
    获取一段时间获取N根bar数据
    :param symbol: str 合约唯一代码
    :param type_: str 数据类型, 如1D_BAR_DEPTH
    :param source: str 行情来源
    :param count: int bar数量, 默认-1, 表示全部返回
    :param fields: list 指定返回对象字段, 默认None, 表示全部返回
    :param data_type: int 数据返回类型, 0 表示pandas结构, 1 表示numpy结构, 2 表示dict结构, 默认1
    :param start_datetime: str bar的开始时间, 支持以下格式'%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S', '%Y%m%d%H%M%S', 默认'', 表示全部返回
    :param end_datetime: str bar的结束时间, 支持以下格式'%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S', '%Y%m%d%H%M%S', 默认'', 表示全部返回

    :return: Bar数据, 数据类型根据入参的data_type值而变
    :return: time(int):时间戳
    :return: open(float):开盘价
    :return: close(float):收盘价
    :return: high(float):最高价
    :return: low(float):最低价
    bars = md.query_bars_pro("EURUSDSP", "1D_BAR_DEPTH",  "UBS_HO", count=-1, data_type=DATA_TYPE_PANDAS, start_datetime="2021-12-10 00:00", end_datetime="2021-12-20 00:00")
    """
    # 解析时间参数
    end_time = None
    if end_datetime:
        # 支持多种时间格式
        time_formats = ['%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S', '%Y%m%d%H%M%S']
        for fmt in time_formats:
            try:
                dt = datetime.strptime(end_datetime, fmt)
                end_time = int(dt.timestamp() * 1000)  # 转换为毫秒级时间戳
                break
            except ValueError:
                continue
        if end_time is None:
            raise ValueError(f"无法解析结束时间: {end_datetime}")
    else:
        now_utc = datetime.now(timezone.utc)
        end_time = int(now_utc.timestamp() * 1000)

    # 根据type_参数确定K线周期
    # 这里假设type_参数如"1D_BAR_DEPTH"中的数字代表周期
    if "1D_BAR_DEPTH" in type_:
        bar_type = 1440  # 日线
    elif "1H_BAR_DEPTH" in type_:
        bar_type = 60  # 60分钟线
    elif "5M_BAR_DEPTH" in type_:
        bar_type = 5  # 5分钟线
    elif "1M_BAR_DEPTH" in type_:
        bar_type = 1  # 1分钟线
    else:
        bar_type = 60  # 默认60分钟线

    # 调用financego接口获取数据
    raw_data = fetch_history_bars_by_financego(symbol, bar_type, source, count, end_time)

    if raw_data is None:
        return None

    # 处理返回的数据
    if 'data' in raw_data:
        bars_data = raw_data['data']
    else:
        bars_data = []

    # 如果有开始时间限制，过滤数据
    if start_datetime:
        start_time = None
        for fmt in time_formats:
            try:
                dt = datetime.strptime(start_datetime, fmt)
                start_time = int(dt.timestamp() * 1000)  # 转换为毫秒级时间戳
                break
            except ValueError:
                continue

        if start_time is not None:
            # 过滤掉开始时间之前的数据
            bars_data = [bar for bar in bars_data if bar['time'] >= start_time]
    # 强制只保留 time, open, close, high, low 字段
    required_fields = ['time', 'open', 'close', 'high', 'low']
    filtered_bars = []
    for bar in bars_data:
        filtered_bar = {field: bar[field] for field in required_fields if field in bar}
        filtered_bars.append(filtered_bar)
    bars_data = filtered_bars

    # 如果指定了字段，进一步过滤到指定字段（但仍限制在required_fields范围内）
    if fields:
        # 只保留用户指定的字段，但必须在required_fields范围内
        allowed_fields = [field for field in fields if field in required_fields]
        if allowed_fields:
            final_filtered_bars = []
            for bar in bars_data:
                filtered_bar = {field: bar[field] for field in allowed_fields if field in bar}
                final_filtered_bars.append(filtered_bar)
            bars_data = final_filtered_bars

    # 根据data_type参数返回不同格式的数据
    if data_type == 0:  # pandas DataFrame
        if not bars_data:
            return pd.DataFrame()
        df = pd.DataFrame(bars_data)
        # 将time列转换为datetime格式
        df['datetime'] = pd.to_datetime(df['time'], unit='ms')
        return df
    elif data_type == 1:  # numpy结构
        return np.array(bars_data)
    elif data_type == 2:  # dict结构
        return bars_data
    else:  # 默认返回dict结构
        return bars_data


def get_price(symbol, type_=None, source=None, fields=None):
    """
    获取最新行情

    :param symbol: str 合约唯一代码
    :param type_: str 数据类型，默认None，使用ODM_DEPTH
    :param source: str 行情来源，默认None，使用MT4
    :param fields: list 指定返回对象字段，默认None，表示全部返回
    :return: list 行情对象列表

    返回格式:
    [{
        'status': '1',  # 价格状态 1-正常, 2-异常
        'source': 'MT4',  # 数据渠道
        'type': 'ODM_DEPTH',  # 数据类型
        'symbol': 'EURUSD',  # 合约代码
        'time': 1598922000100,  # 时间戳
        'best_bid': 1.19907,  # 最优买价
        'best_bid_amt': 94,  # 最优买价数量
        'best_ask': 1.19919,  # 最优卖价
        'best_ask_amt': 94,  # 最优卖价数量
        'asks': [1.19919, 1.19925, ...],  # 卖出报盘价格
        'ask_vols': [94, 94, ...],  # 卖出报盘数量
        'bids': [1.19907, 1.19901, ...],  # 买入报盘价格
        'bid_vols': [94, 94, ...],  # 买入报盘数量
        'limit_up': '',  # 涨停价
        'limit_down': ''  # 跌停价
    }]
    """

    try:
        # 调用底层接口获取实时价格数据
        latest_bar = fetch_latest_price_from_bars(symbol, source)

        if latest_bar is None:
            return []

        # 定义所有可能的字段
        all_fields = [
            'status', 'source', 'type', 'symbol', 'time',
            'best_bid', 'best_bid_amt', 'best_ask', 'best_ask_amt',
            'asks', 'ask_vols', 'bids', 'bid_vols',
            'limit_up', 'limit_down'
        ]

        # 处理字段筛选
        if fields is not None:
            # 只保留用户指定的字段，但必须在all_fields范围内
            allowed_fields = [field for field in fields if field in all_fields]
        else:
            # 如果没有指定字段，返回所有字段
            allowed_fields = all_fields

        # 过滤数据字段
        filtered_data_list = []

        # 只保留允许的字段
        filtered_data = {}
        for field in allowed_fields:
            if field in latest_bar:
                filtered_data[field] = latest_bar[field]
            else:
                # 为缺失的字段设置默认值
                if field in ['status']:
                    filtered_data[field] = '1'  # 默认正常状态
                elif field in ['source']:
                    filtered_data[field] = source
                elif field in ['type']:
                    filtered_data[field] = type_
                elif field in ['symbol']:
                    # todo 最有卖价最优卖价等价格
                    filtered_data[field] = ''
        filtered_data_list.append(filtered_data)

        return filtered_data_list

    except Exception as e:
        print(f"获取价格数据失败: {e}")
        return []


if __name__ == '__main__':
    # 测试代码
    print("测试获取EURUSD价格:")
    price_data = get_price("EURUSD")
    print(price_data)

    print("\n测试获取指定字段:")
    price_data_filtered = get_price("EURUSD", fields=['symbol', 'best_bid', 'best_ask', 'time'])
    print(price_data_filtered)
