
"""
获取逐笔持仓信息API

根据API文档 html/fx/pos.html#pos.get_ord_position 实现的持仓查询接口
"""

import logging
from typing import List, Dict, Any, Optional, Union

from custom_api.dao import get_position_dao
from custom_api.models import Position, PositionSide, PositionStatus

logger = logging.getLogger(__name__)


def get_ord_position(order_id: Optional[Union[str, int]] = None,
                    symbol: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    根据合约或者开仓订单ID获取逐笔持仓信息，如果不传获取全部未平仓持仓

    该函数实现了外汇交易系统的持仓查询功能，支持按开仓订单ID或合约代码查询持仓信息。
    如果不传任何参数，则返回所有未平仓的活跃持仓。

    Args:
        order_id (Optional[Union[str, int]]): 开仓订单ID，可以是字符串或整数类型
            - 当提供此参数时，返回指定订单对应的持仓信息
            - 默认值: None

        symbol (Optional[str]): 合约代码，如 'EURUSD', 'XAUUSD' 等
            - 当提供此参数时，返回指定合约的所有持仓信息
            - 默认值: None

    Returns:
        List[Dict[str, Any]]: 持仓信息列表，每个元素包含以下字段：
            - id (int): 唯一编号，持仓的数据库主键ID
            - symbol (str): 合约代码，如 'EURUSDSP', 'XAUUSD' 等
            - frozenQuantity (float): 冻结量，当前被冻结无法交易的持仓数量
            - quantity (float): 总持仓量，当前持仓的总数量
            - quantityTd (float): 今日持仓量，今日新开的持仓数量
            - posSide (int): 头寸方向，枚举值：
                * 0: 中性（无持仓）
                * 1: 多方向（多头持仓）
                * 2: 空方向（空头持仓）
            - profit (float): 损益，总盈亏金额（已实现 + 未实现 - 费用）
            - value (float): 估值，持仓的当前市值
            - costPrice (float): 敞口价格，开仓的平均价格
            - unRealizedPL (float): 未交割浮动损益，未实现盈亏
            - realizedPL (float): 已交割损益，已实现盈亏
            - washAmount (float): 持仓成本，开仓时的总成本
            - time (int): 头寸时间，开仓时间的时间戳（毫秒）

    Raises:
        ValueError: 当参数类型不正确时抛出
        Exception: 当数据库查询失败时抛出

    Examples:
        # 获取所有未平仓持仓
        >>> positions = get_ord_position()
        >>> print(f"共有 {len(positions)} 个持仓")

        # 根据订单ID查询持仓
        >>> positions = get_ord_position(order_id="216868121676222464")
        >>> if positions:
        ...     pos = positions[0]
        ...     print(f"持仓合约: {pos['symbol']}, 数量: {pos['quantity']}")

        # 根据合约代码查询持仓
        >>> positions = get_ord_position(symbol="EURUSD")
        >>> for pos in positions:
        ...     print(f"持仓ID: {pos['id']}, 盈亏: {pos['profit']}")

        # 同时传入两个参数（优先使用order_id）
        >>> positions = get_ord_position(order_id="123456", symbol="XAUUSD")
        >>> # 实际只会按order_id查询

    Note:
        1. 当同时传入 order_id 和 symbol 时，优先使用 order_id 进行查询
        2. 返回的时间戳为毫秒级别，需要除以1000转换为秒级时间戳
        3. posSide 字段的枚举值与 PositionSide 模型对应：
           - NEUTRAL(0) -> 0
           - LONG(1) -> 1
           - SHORT(2) -> 2
        4. 只返回活跃状态的持仓（is_active=True）
        5. 如果查询结果为空，返回空列表 []
    """
    try:
        # 参数验证
        if order_id is not None and not isinstance(order_id, (str, int)):
            raise ValueError(f"order_id 参数类型错误，期望 str 或 int，实际 {type(order_id)}")

        if symbol is not None and not isinstance(symbol, str):
            raise ValueError(f"symbol 参数类型错误，期望 str，实际 {type(symbol)}")

        # 获取持仓DAO实例
        position_dao = get_position_dao()

        # 根据参数查询持仓
        positions = []

        if order_id is not None:
            # 优先按订单ID查询
            order_id_str = str(order_id)
            logger.debug(f"按订单ID查询持仓: {order_id_str}")

            position = position_dao.get_by_open_order_id(order_id_str)
            if position and position.is_active:
                positions = [position]

        elif symbol is not None:
            # 按合约代码查询
            logger.debug(f"按合约代码查询持仓: {symbol}")

            # 获取指定合约的活跃持仓
            all_positions = position_dao.get_by_symbol(symbol)
            positions = [pos for pos in all_positions if pos.is_active and pos.position_status == PositionStatus.OPEN]

        else:
            # 获取所有未平仓持仓
            logger.debug("获取所有未平仓持仓")
            positions = position_dao.get_open_positions()

        # 转换为API格式
        result = []
        for position in positions:
            position_dict = _convert_position_to_api_format(position)
            result.append(position_dict)

        logger.info(f"查询持仓完成: order_id={order_id}, symbol={symbol}, 结果数量={len(result)}")
        return result

    except ValueError as e:
        logger.error(f"参数验证失败: {e}")
        raise

    except Exception as e:
        logger.error(f"查询持仓失败: {e}")
        raise Exception(f"获取持仓信息失败: {str(e)}")


def _convert_position_to_api_format(position: Position) -> Dict[str, Any]:
    """
    将Position模型转换为API返回格式

    Args:
        position: Position模型实例

    Returns:
        Dict[str, Any]: API格式的持仓信息
    """
    try:
        # 转换持仓方向枚举
        pos_side_map = {
            PositionSide.NEUTRAL: 0,
            PositionSide.LONG: 1,
            PositionSide.SHORT: 2
        }

        pos_side = pos_side_map.get(position.position_side, 0)

        # 计算总盈亏（已实现 + 未实现 - 手续费 - 隔夜利息）
        total_profit = (
            (position.realized_pnl or 0) +
            (position.unrealized_pnl or 0) -
            (position.commission or 0) -
            (position.swap_fee or 0)
        )

        # 转换时间为毫秒时间戳
        time_timestamp = 0
        if position.open_time:
            time_timestamp = int(position.open_time.timestamp() * 1000)

        return {
            'id': position.id,
            'symbol': position.symbol or '',
            'frozenQuantity': float(position.frozen_quantity or 0),
            'quantity': float(position.total_quantity or 0),
            'quantityTd': float(position.today_quantity or 0),
            'posSide': pos_side,
            'profit': float(total_profit),
            'value': float(position.market_value or 0),
            'costPrice': float(position.open_price or 0),
            'unRealizedPL': float(position.unrealized_pnl or 0),
            'realizedPL': float(position.realized_pnl or 0),
            'washAmount': float(position.position_cost or 0),
            'time': time_timestamp
        }

    except Exception as e:
        logger.error(f"转换持仓格式失败: {e}")
        # 返回默认格式，避免整个查询失败
        return {
            'id': position.id if hasattr(position, 'id') else 0,
            'symbol': getattr(position, 'symbol', ''),
            'frozenQuantity': 0.0,
            'quantity': 0.0,
            'quantityTd': 0.0,
            'posSide': 0,
            'profit': 0.0,
            'value': 0.0,
            'costPrice': 0.0,
            'unRealizedPL': 0.0,
            'realizedPL': 0.0,
            'washAmount': 0.0,
            'time': 0
        }