from datetime import datetime

import requests


def fetch_history_bars_by_financego(symbol, type_, source="MT4", count=-1, end_time=None):
    """
    通过financego接口获取K线数据（基于结束时间）

    :param symbol: 合约代码，如EURUSD
    :param type_: K线类型，如1, 5, 15, 30, 60（分钟）
    :param source: 数据源，如MT4
    :param count: 获取的K线数量，默认-1表示全部
    :param end_time: 结束时间戳（毫秒），默认None表示当前时间
    :return: K线数据列表
    """
    # 根据curl命令分析，构建请求URL和参数
    url = 'https://financego.x-funds.com/infos/ebar/getHisBarListWithCount.action'

    # 处理结束时间，如果未提供则使用当前时间
    if end_time is None:
        import time
        end_time = int(time.time() * 1000)  # 当前时间毫秒级时间戳

    # 构造请求参数
    params = {
        'source': source,
        'symbol': symbol,
        'count': count if count != -1 else 1000,  # 默认获取1000条
        'type': type_,  # K线周期，如60表示60分钟
        'endTime': end_time,
        '_': int(datetime.now().timestamp() * 1000)  # 当前时间戳作为缓存busting参数
    }

    # 设置请求头
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Referer': 'https://financego.x-funds.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'titanOne-client': 'titanOne-client',
        'x-requested-with': 'x-requested-with, XMLHttpRequest'
    }

    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()  # 检查HTTP错误

        # 解析JSON响应
        data = response.json()
        # 返回数据
        return data
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except Exception as e:
        print(f"数据处理错误: {e}")
        return None


def fetch_bars_by_financego(symbol, type_, source="MT4", count=-1, startTime=-1):
    """
    通过financego接口获取K线数据（基于开始时间）

    :param symbol: 合约代码，如EURUSD
    :param type_: K线类型，如1, 5, 15, 30, 60（分钟）, 10080（周线）
    :param source: 数据源，如MT4
    :param count: 获取的K线数量，默认-1表示全部
    :param startTime: 开始时间戳（毫秒），默认-1表示获取最新价格
    :return: K线数据列表
    """
    # 根据curl命令分析，构建请求URL和参数
    url = 'https://financego.x-funds.com/infos/ebar/getBarListByPrevTimeWithRate.action'

    # 处理开始时间，如果未提供则使用默认时间
    if startTime is None:
        import time
        # 使用一个较早的时间作为默认开始时间（例如30天前）
        startTime = int((time.time() - 30 * 24 * 3600) * 1000)  # 30天前的毫秒级时间戳

    # 构造请求参数
    params = {
        'source': source,
        'symbol': symbol,
        'count': count if count != -1 else 1001,  # 默认获取1001条，参考curl中的count值
        'type': type_,  # K线周期，如60表示60分钟，10080表示周线
        'startTime': startTime,
        '_': int(datetime.now().timestamp() * 1000)  # 当前时间戳作为缓存busting参数
    }

    # 设置请求头（与curl请求保持一致）
    headers = {
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'Connection': 'keep-alive',
        'DNT': '1',
        'Referer': 'https://financego.x-funds.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'titanOne-client': 'titanOne-client',
        'x-requested-with': 'x-requested-with, XMLHttpRequest'
    }

    # 注意：curl中包含cookie，但在实际使用中可能需要处理session
    # 如果需要cookie，可以添加：
    # cookies = {'JSESSIONID': 'your_session_id'}

    try:
        # 发送GET请求
        response = requests.get(url, params=params, headers=headers)
        response.raise_for_status()  # 检查HTTP错误

        # 解析JSON响应
        data = response.json()
        # 返回数据
        return data
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    except Exception as e:
        print(f"数据处理错误: {e}")
        return None


def fetch_latest_price_from_bars(symbol, source="MT4"):
    """
    通过获取最新的1分钟K线数据来模拟实时价格

    :param symbol: 合约代码
    :param source: 数据源
    :return: 模拟的实时价格数据
    """
    try:
        # 获取最新的1分钟K线数据，使用与测试成功相同的参数
        market_data = fetch_bars_by_financego(
            symbol=symbol,
            type_=1,  # 1分钟K线
            source=source,
            count=1,
            startTime=-1  # 使用-1获取最新数据
        )

        if market_data and 'data' in market_data and market_data['data']['bars']:
            bars = market_data['data']['bars']
            latest_bar = max(bars, key=lambda x: x['time'])  # 获取最新的K线数据
            return latest_bar
        return None
    except Exception as e:
        print(f"从K线数据获取最新价格失败: {e}")
        return None


if __name__ == '__main__':
    # 测试实时价格获取
    price_data = fetch_latest_price_from_bars("EURUSD", "MT4")
    print("实时价格数据:", price_data)
